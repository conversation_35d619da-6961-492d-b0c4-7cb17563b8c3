<template>
  <div class="checkout-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">房务管理 - 退房管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理客户退房流程，查看退房记录历史</p>
          </div>
        </div>
        <div class="flex items-center gap-3">
          <el-button
            type="primary"
            @click="handleNewCheckout"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
          >
            <el-icon class="mr-2"><Plus /></el-icon>
            办理退房
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <FilterPanel :fields="filterFields" :filters="searchFilters" @search="handleSearch" />

    <!-- 退房记录列表表格 -->
    <CheckoutRecordTable
      :data="tableData"
      :loading="tableLoading"
      :pagination="pagination"
      @view="handleView"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 退房记录详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="dialogTitle"
      width="900px"
      align-center
      :close-on-click-modal="false"
      class="checkout-detail-dialog"
    >
      <div class="max-h-[70vh] overflow-y-auto" v-loading="detailLoading">
        <div class="detail-content">
          <!-- 基本信息 -->
          <div class="detail-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
              <h3 class="section-title">退房基本信息</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="detail-item">
                <span class="detail-label">退房记录ID：</span>
                <span class="detail-value font-mono">{{ detailData.rid }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">入住记录ID：</span>
                <span class="detail-value font-mono">{{ detailData.aid }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">产妇姓名：</span>
                <span class="detail-value font-medium">{{ detailData.maternity_name }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">房间号：</span>
                <el-tag type="success" size="large">{{ detailData.room_number }}</el-tag>
              </div>
              <div class="detail-item">
                <span class="detail-label">入住日期：</span>
                <span class="detail-value">{{ detailData.check_in_date }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">预计退房日期：</span>
                <span class="detail-value">{{ detailData.expected_checkout_date }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">实际退房日期：</span>
                <span class="detail-value font-medium text-green-600">{{ detailData.actual_checkout_date }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">退房状态：</span>
                <el-tag :type="getCheckoutStatusType(detailData.checkout_status)" size="large">
                  {{ detailData.checkout_status }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 退房结算表单弹窗 -->
    <el-dialog
      v-model="formVisible"
      :title="formDialogTitle"
      width="900px"
      align-center
      :before-close="handleFormClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="checkout-dialog"
    >
      <div class="max-h-[70vh] overflow-y-auto" v-loading="formLoading">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="140px"
          class="checkout-form"
        >
          <!-- 客户与房间信息 -->
          <div class="form-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
              <h3 class="section-title">客户与房间信息</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <el-form-item label="客户姓名">
                <span class="text-gray-800 font-medium">{{ formData.customerName }}</span>
              </el-form-item>
              <el-form-item label="房间号">
                <el-tag type="success" size="large">{{ formData.roomNumber }}</el-tag>
              </el-form-item>
              <el-form-item label="入住日期">
                <span class="text-gray-700">{{ formData.checkinDate }}</span>
              </el-form-item>
              <el-form-item label="原定退房日期">
                <span class="text-gray-700">{{ formData.originalCheckoutDate }}</span>
              </el-form-item>
            </div>
          </div>

          <!-- 费用明细区 -->
          <div class="form-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="section-header flex items-center justify-between mb-6 pb-3 border-b border-gray-200">
              <h3 class="section-title">费用明细</h3>
              <el-tooltip content="如需修改费用信息请前往结算单管理" placement="top">
                <el-icon class="text-gray-400 cursor-help"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>

            <!-- 费用明细卡片 -->
            <div class="cost-detail-card bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div class="grid grid-cols-2 gap-x-16 gap-y-4">
                <div class="cost-item">
                  <span class="cost-label">套餐名称：</span>
                  <span class="cost-value">{{ formData.packageName || '-' }}</span>
                </div>
                <div class="cost-item">
                  <span class="cost-label">入住天数：</span>
                  <span class="cost-value">{{ formData.stayDays || 0 }}天</span>
                </div>
                <div class="cost-item">
                  <span class="cost-label">套餐价格：</span>
                  <span class="cost-value">¥{{ formData.packagePrice || '0.00' }}</span>
                </div>
                <div class="cost-item">
                  <span class="cost-label">押金金额：</span>
                  <span class="cost-value">¥{{ formData.depositAmount || '0.00' }}</span>
                </div>
                <div class="cost-item">
                  <span class="cost-label">定金金额：</span>
                  <span class="cost-value">¥{{ formData.earnestAmount || '0.00' }}</span>
                </div>
                <div class="cost-item">
                  <span class="cost-label">应付金额：</span>
                  <span class="cost-value">¥{{ formData.payableAmount || '0.00' }}</span>
                </div>
                <div class="cost-item">
                  <span class="cost-label">已付金额：</span>
                  <span class="cost-value">¥{{ formData.paidAmount || '0.00' }}</span>
                </div>
                <div class="cost-item">
                  <span class="cost-label">剩余金额：</span>
                  <span class="cost-value" :class="getRemainingAmountColor(formData.remainingAmount)">
                    ¥{{ formData.remainingAmount || '0.00' }}
                  </span>
                </div>
                <div class="cost-item">
                  <span class="cost-label">支付状态：</span>
                  <el-tag :type="getPaymentStatusType(formData.paymentStatus)" size="large">
                    {{ getPaymentStatusText(formData.paymentStatus) }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 提示信息 -->
            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div class="flex items-center">
                <el-icon class="text-yellow-600 mr-2"><Warning /></el-icon>
                <span class="text-sm text-yellow-700">如需修改费用信息，请前往结算单管理进行操作</span>
              </div>
            </div>
          </div>

          <!-- 退房信息确认 -->
          <div class="form-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
              <h3 class="section-title">退房信息确认</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <el-form-item label="实际退房日期" prop="actualCheckoutDate" required>
                <el-date-picker
                  v-model="formData.actualCheckoutDate"
                  type="date"
                  placeholder="选择实际退房日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="w-full"
                />
              </el-form-item>
              <el-form-item label="设施设备检查情况" prop="itemCheck" class="col-span-full">
                <el-input
                  v-model="formData.itemCheck"
                  type="textarea"
                  :rows="4"
                  placeholder="记录房间设施设备检查情况，是否有损坏及赔偿等"
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleFormClose" :disabled="formSubmitting">取消</el-button>
          <el-button
            type="primary"
            @click="handleFormSubmit"
            :loading="formSubmitting"
            class="bg-green-500 hover:bg-green-600 border-green-500"
          >
            <el-icon class="mr-2"><Check /></el-icon>
            {{ formSubmitting ? '处理中...' : '确认结算并退房' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 在住产妇选择弹窗 -->
    <el-dialog
      v-model="showMaternityDialog"
      title="选择在住产妇"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="maternity-selection">
        <div class="mb-4">
          <p class="text-gray-600">请选择需要办理退房的在住产妇：</p>
        </div>

        <div v-loading="maternityLoading" class="min-h-[200px]">
          <el-table
            :data="maternityList"
            @row-click="handleSelectMaternity"
            highlight-current-row
            class="w-full"
          >
            <el-table-column prop="maternity_name" label="产妇姓名" />
            <el-table-column prop="room_number" label="房间号" width="100">
              <template #default="{ row }">
                <el-tag type="success">{{ row.room_number }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="check_in_date" label="入住日期" />
            <el-table-column prop="expected_checkout_date" label="预计退房日期" />
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="handleSelectMaternity(row)"
                >
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMaternityDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Check, InfoFilled, Warning } from '@element-plus/icons-vue'
import CheckoutRecordTable from '@/views/housekeeping/components/CheckoutRecordTable.vue'
import FilterPanel from '@/components/FilterPanel.vue'
import { get, post } from '@/utils/request.js'

// 搜索过滤条件
const searchFilters = reactive({
  status: '',
  ec: '', // 预计退房日期
  sk: '', // 搜索关键字
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'status',
    type: 'select',
    label: '退房状态',
    placeholder: '选择退房状态',
    options: [
      { label: '延期退房', value: 'DELAY_CHECKOUT' },
      { label: '正常退房', value: 'NORMAL_CHECKOUT' },
      { label: '提前退房', value: 'EARLY_CHECKOUT' },
    ],
  },
  {
    key: 'ec',
    type: 'date',
    label: '预计退房日期',
    placeholder: '选择预计退房日期',
  },
  {
    key: 'sk',
    type: 'input',
    label: '产妇姓名/房号',
    placeholder: '输入产妇姓名或房间号',
  },
]

// 表格数据
const tableData = ref([])
const tableLoading = ref(false)

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 20,
  total_count: 0,
  total_page: 0,
})

// 详情弹窗相关
const detailVisible = ref(false)
const detailData = ref({})
const detailLoading = ref(false)

// 表单相关
const formVisible = ref(false)
const formData = ref({})
const formRef = ref()
const formLoading = ref(false)
const formSubmitting = ref(false)

// 在住产妇选择相关
const showMaternityDialog = ref(false)
const maternityList = ref([])
const maternityLoading = ref(false)

// Dialog标题
const dialogTitle = computed(() => {
  return detailData.value?.maternity_name ? `退房记录详情 - ${detailData.value.maternity_name}` : '退房记录详情'
})

const formDialogTitle = computed(() => {
  return formData.value?.customerName ? `退房结算 - ${formData.value.customerName}` : '退房结算'
})

// 表单验证规则
const formRules = {
  actualCheckoutDate: [
    { required: true, message: '请选择实际退房日期', trigger: 'change' }
  ],
  itemCheck: [
    { required: true, message: '请填写设施设备检查情况', trigger: 'blur' }
  ]
}

// 获取退房记录列表
const fetchCheckoutList = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.page_size,
      ...searchFilters
    }

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await get('customer-service/checkout/list', params)

    tableData.value = response.list || []
    pagination.page = response.page || 1
    pagination.page_size = response.page_size || 20
    pagination.total_count = response.total_count || 0
    pagination.total_page = response.total_page || 0
  } catch (error) {
    console.error('获取退房记录列表失败:', error)
    ElMessage.error('获取退房记录列表失败')
    tableData.value = []
  } finally {
    tableLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  fetchCheckoutList()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pagination.page_size = newSize
  pagination.page = 1
  fetchCheckoutList()
}

const handleCurrentChange = (newPage) => {
  pagination.page = newPage
  fetchCheckoutList()
}

// 获取退房状态标签类型
const getCheckoutStatusType = (status) => {
  const typeMap = {
    '延期退房': 'warning',
    '正常退房': 'success',
    '提前退房': 'info',
  }
  return typeMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status) => {
  const statusMap = {
    'UNPAID': '未支付',
    'PARTIAL_PAID': '部分支付',
    'PAID': '已支付',
    'REFUNDED': '已退款',
    'OVERDUE': '逾期'
  }
  return statusMap[status] || status || '未知'
}

// 获取支付状态标签类型
const getPaymentStatusType = (status) => {
  const typeMap = {
    'UNPAID': 'danger',
    'PARTIAL_PAID': 'warning',
    'PAID': 'success',
    'REFUNDED': 'info',
    'OVERDUE': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取剩余金额颜色
const getRemainingAmountColor = (amount) => {
  const numAmount = parseFloat(amount || 0)
  if (numAmount > 0) {
    return 'text-red-600'    // 大于0：红色（需要补交）
  } else if (numAmount === 0) {
    return 'text-green-600'  // 等于0：绿色（已结清）
  } else {
    return 'text-yellow-600' // 小于0：黄色（需要退款）
  }
}

// 办理退房 - 打开在住产妇选择弹窗
const handleNewCheckout = async () => {
  showMaternityDialog.value = true
  await fetchMaternityList()
}

// 获取在住产妇列表
const fetchMaternityList = async () => {
  maternityLoading.value = true
  try {
    const response = await get('customer-service/checkout/maternity/list/')
    maternityList.value = response || []
  } catch (error) {
    console.error('获取在住产妇列表失败:', error)
    ElMessage.error('获取在住产妇列表失败')
    maternityList.value = []
  } finally {
    maternityLoading.value = false
  }
}

// 选择产妇进入退房流程
const handleSelectMaternity = (maternity) => {
  // 关闭选择弹窗
  showMaternityDialog.value = false

  // 构造退房表单数据，包含费用信息
  const costInfo = maternity.cost_info || {}
  const packageInfo = costInfo.package || {}

  const checkoutData = {
    id: maternity.aid,
    customerName: maternity.maternity_name,
    roomNumber: maternity.room_number,
    checkinDate: maternity.check_in_date,
    originalCheckoutDate: maternity.expected_checkout_date,
    actualCheckoutDate: new Date().toISOString().slice(0, 10),
    status: 'pending_checkout',

    // 费用信息
    packageName: packageInfo.name || '',
    packageDescription: packageInfo.description || '',
    stayDays: packageInfo.stay_days || 0,
    packagePrice: costInfo.package_price || '0.00',
    payableAmount: costInfo.payable_amount || '0.00',
    paidAmount: costInfo.paid_amount || '0.00',
    remainingAmount: costInfo.remaining_amount || '0.00',
    depositAmount: costInfo.deposit_amount || '0.00',
    earnestAmount: costInfo.earnest_amount || '0.00',
    paymentStatus: costInfo.payment_status || '',
    paymentMethod: costInfo.payment_method || [],
    remark: costInfo.remark || '',

    // 其他字段
    otherCharges: '',
    depositReturn: 'full',
    depositDeductionReason: '',
    itemCheck: '',
    clientSignature: '',
  }

  // 打开退房表单
  formData.value = checkoutData
  formVisible.value = true

  ElMessage.success(`已选择产妇：${maternity.maternity_name}，开始办理退房`)
}

// 表单关闭处理
const handleFormClose = () => {
  formVisible.value = false
}

// 表单提交
const handleFormSubmit = async () => {
  if (!formRef.value || formSubmitting.value) return

  try {
    await formRef.value.validate()

    const confirmed = await ElMessageBox.confirm(
      '确认完成退房结算吗？此操作不可撤销。',
      '确认退房',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    if (confirmed) {
      formSubmitting.value = true

      // 这里可以添加实际的API调用
      // await post('customer-service/checkout/process/', formData.value)

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      formVisible.value = false
      ElMessage.success('退房结算完成')

      // 刷新列表数据
      await fetchCheckoutList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退房结算失败:', error)
      ElMessage.error('退房结算失败')
    }
  } finally {
    formSubmitting.value = false
  }
}

// 查看详情
const handleView = (row) => {
  detailData.value = row
  detailVisible.value = true
}

onMounted(() => {
  fetchCheckoutList()
})
</script>

<style scoped>
.checkout-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

.checkout-detail-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.checkout-detail-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

.checkout-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.checkout-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

.checkout-form {
  color: #374151;
}

.checkout-form .form-section {
  transition: all 0.3s ease;
}

.checkout-form .form-section:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.detail-section {
  transition: all 0.3s ease;
}

.detail-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.detail-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}

/* 费用明细卡片样式 */
.cost-detail-card {
  transition: all 0.3s ease;
}

.cost-detail-card:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.cost-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.cost-value {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

/* 剩余金额颜色优先级 */
.cost-value.text-red-600 {
  color: #dc2626 !important;
}

.cost-value.text-green-600 {
  color: #16a34a !important;
}

.cost-value.text-yellow-600 {
  color: #ca8a04 !important;
}
</style>
