<template>
  <div class="checkout-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">房务管理 - 退房记录</h1>
            <p class="text-sm text-gray-600 mt-1">查看所有退房记录历史信息</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <FilterPanel :fields="filterFields" :filters="searchFilters" @search="handleSearch" />

    <!-- 退房记录列表表格 -->
    <CheckoutRecordTable
      :data="tableData"
      :loading="tableLoading"
      :pagination="pagination"
      @view="handleView"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 退房记录详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="dialogTitle"
      width="900px"
      align-center
      :close-on-click-modal="false"
      class="checkout-detail-dialog"
    >
      <div class="max-h-[70vh] overflow-y-auto" v-loading="detailLoading">
        <div class="detail-content">
          <!-- 基本信息 -->
          <div class="detail-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
              <h3 class="section-title">退房基本信息</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="detail-item">
                <span class="detail-label">退房记录ID：</span>
                <span class="detail-value font-mono">{{ detailData.rid }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">入住记录ID：</span>
                <span class="detail-value font-mono">{{ detailData.aid }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">产妇姓名：</span>
                <span class="detail-value font-medium">{{ detailData.maternity_name }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">房间号：</span>
                <el-tag type="success" size="large">{{ detailData.room_number }}</el-tag>
              </div>
              <div class="detail-item">
                <span class="detail-label">入住日期：</span>
                <span class="detail-value">{{ detailData.check_in_date }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">预计退房日期：</span>
                <span class="detail-value">{{ detailData.expected_checkout_date }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">实际退房日期：</span>
                <span class="detail-value font-medium text-green-600">{{ detailData.actual_checkout_date }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">退房状态：</span>
                <el-tag :type="getCheckoutStatusType(detailData.checkout_status)" size="large">
                  {{ detailData.checkout_status }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CheckoutRecordTable from '@/views/housekeeping/components/CheckoutRecordTable.vue'
import FilterPanel from '@/components/FilterPanel.vue'
import { get } from '@/utils/request.js'

// 搜索过滤条件
const searchFilters = reactive({
  status: '',
  ec: '', // 预计退房日期
  sk: '', // 搜索关键字
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'status',
    type: 'select',
    label: '退房状态',
    placeholder: '选择退房状态',
    options: [
      { label: '延期退房', value: 'DELAY_CHECKOUT' },
      { label: '正常退房', value: 'NORMAL_CHECKOUT' },
      { label: '提前退房', value: 'EARLY_CHECKOUT' },
    ],
  },
  {
    key: 'ec',
    type: 'date',
    label: '预计退房日期',
    placeholder: '选择预计退房日期',
  },
  {
    key: 'sk',
    type: 'input',
    label: '产妇姓名/房号',
    placeholder: '输入产妇姓名或房间号',
  },
]

// 表格数据
const tableData = ref([])
const tableLoading = ref(false)

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 20,
  total_count: 0,
  total_page: 0,
})

// 详情弹窗相关
const detailVisible = ref(false)
const detailData = ref({})
const detailLoading = ref(false)

// Dialog标题
const dialogTitle = computed(() => {
  return detailData.value?.maternity_name ? `退房记录详情 - ${detailData.value.maternity_name}` : '退房记录详情'
})

// 获取退房记录列表
const fetchCheckoutList = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.page_size,
      ...searchFilters
    }

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await get('customer-service/checkout/list', params)

    tableData.value = response.list || []
    pagination.page = response.page || 1
    pagination.page_size = response.page_size || 20
    pagination.total_count = response.total_count || 0
    pagination.total_page = response.total_page || 0
  } catch (error) {
    console.error('获取退房记录列表失败:', error)
    ElMessage.error('获取退房记录列表失败')
    tableData.value = []
  } finally {
    tableLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  fetchCheckoutList()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pagination.page_size = newSize
  pagination.page = 1
  fetchCheckoutList()
}

const handleCurrentChange = (newPage) => {
  pagination.page = newPage
  fetchCheckoutList()
}

// 获取退房状态标签类型
const getCheckoutStatusType = (status) => {
  const typeMap = {
    '延期退房': 'warning',
    '正常退房': 'success',
    '提前退房': 'info',
  }
  return typeMap[status] || 'info'
}

// 查看详情
const handleView = (row) => {
  detailData.value = row
  detailVisible.value = true
}

onMounted(() => {
  fetchCheckoutList()
})
</script>

<style scoped>
.checkout-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

.checkout-detail-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.checkout-detail-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.detail-section {
  transition: all 0.3s ease;
}

.detail-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.detail-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
