<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="1000px"
    align-center
    :close-on-click-modal="false"
    class="checkout-detail-dialog"
    @close="handleClose"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <div class="detail-content" v-if="detailData">
        <!-- 产妇基本信息 -->
        <div class="detail-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
            <h3 class="section-title">产妇基本信息</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="detail-item">
              <span class="detail-label">产妇姓名：</span>
              <span class="detail-value font-medium">{{ detailData.maternity_info?.name }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">联系电话：</span>
              <span class="detail-value">{{ detailData.maternity_info?.phone }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">性别：</span>
              <span class="detail-value">{{ detailData.maternity_info?.gender_display }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">出生日期：</span>
              <span class="detail-value">{{ detailData.maternity_info?.birth_date }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">籍贯：</span>
              <span class="detail-value">{{ detailData.maternity_info?.native_place }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">家庭住址：</span>
              <span class="detail-value">{{ detailData.maternity_info?.home_address }}</span>
            </div>
          </div>
        </div>

        <!-- 入住信息 -->
        <div class="detail-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
            <h3 class="section-title">入住信息</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="detail-item">
              <span class="detail-label">入住记录ID：</span>
              <span class="detail-value font-mono">{{ detailData.admission_info?.aid }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">房间号：</span>
              <el-tag type="success" size="large">{{ detailData.admission_info?.room_number }}</el-tag>
            </div>
            <div class="detail-item">
              <span class="detail-label">入住日期：</span>
              <span class="detail-value">{{ detailData.admission_info?.check_in_date }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">预计退房日期：</span>
              <span class="detail-value">{{ detailData.admission_info?.expected_checkout_date }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">实际退房日期：</span>
              <span class="detail-value font-medium text-green-600">{{ detailData.admission_info?.actual_checkout_date }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">退房状态：</span>
              <el-tag :type="getCheckoutStatusType(detailData.checkout_status)" size="large">
                {{ detailData.checkout_status }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 套餐信息 -->
        <div class="detail-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
            <h3 class="section-title">套餐信息</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="detail-item">
              <span class="detail-label">套餐名称：</span>
              <span class="detail-value font-medium">{{ detailData.cost_info?.package?.name }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">套餐价格：</span>
              <span class="detail-value">¥{{ detailData.cost_info?.package?.price }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">入住天数：</span>
              <span class="detail-value">{{ detailData.cost_info?.package?.stay_days }}天</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">套餐状态：</span>
              <el-tag :type="detailData.cost_info?.package?.status === 'ACTIVE' ? 'success' : 'info'" size="small">
                {{ detailData.cost_info?.package?.status === 'ACTIVE' ? '有效' : '无效' }}
              </el-tag>
            </div>
            <div class="detail-item col-span-full">
              <span class="detail-label">套餐描述：</span>
              <span class="detail-value">{{ detailData.cost_info?.package?.description }}</span>
            </div>
          </div>
        </div>

        <!-- 费用明细 -->
        <div class="detail-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
            <h3 class="section-title">费用明细</h3>
          </div>
          <div class="cost-detail-card bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="grid grid-cols-2 gap-x-16 gap-y-4">
              <div class="cost-item">
                <span class="cost-label">套餐价格：</span>
                <span class="cost-value">¥{{ detailData.cost_info?.package_price }}</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">押金金额：</span>
                <span class="cost-value">¥{{ detailData.cost_info?.deposit_amount }}</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">定金金额：</span>
                <span class="cost-value">¥{{ detailData.cost_info?.earnest_amount }}</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">应付金额：</span>
                <span class="cost-value">¥{{ detailData.cost_info?.payable_amount }}</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">已付金额：</span>
                <span class="cost-value">¥{{ detailData.cost_info?.paid_amount }}</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">剩余金额：</span>
                <span class="cost-value" :class="getRemainingAmountColor(detailData.cost_info?.remaining_amount)">
                  ¥{{ detailData.cost_info?.remaining_amount }}
                </span>
              </div>
              <div class="cost-item">
                <span class="cost-label">支付状态：</span>
                <el-tag :type="getPaymentStatusType(detailData.cost_info?.payment_status)" size="large">
                  {{ getPaymentStatusText(detailData.cost_info?.payment_status) }}
                </el-tag>
              </div>
              <div class="cost-item">
                <span class="cost-label">支付方式：</span>
                <div class="flex flex-wrap gap-1">
                  <el-tag 
                    v-for="method in detailData.cost_info?.payment_method" 
                    :key="method" 
                    size="small"
                    type="info"
                  >
                    {{ getPaymentMethodText(method) }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div v-if="detailData.cost_info?.remark" class="mt-4 pt-4 border-t border-blue-200">
              <div class="cost-item">
                <span class="cost-label">备注：</span>
                <span class="cost-value">{{ detailData.cost_info?.remark }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 退房信息 -->
        <div class="detail-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
            <h3 class="section-title">退房信息</h3>
          </div>
          <div class="grid grid-cols-1 gap-6">
            <div class="detail-item">
              <span class="detail-label">退房记录ID：</span>
              <span class="detail-value font-mono">{{ detailData.rid }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">设施设备检查：</span>
              <span class="detail-value">{{ detailData.facility_equipment_check || '无' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  checkoutId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const dialogTitle = computed(() => {
  return detailData.value?.maternity_info?.name 
    ? `退房详情 - ${detailData.value.maternity_info.name}` 
    : '退房详情'
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.checkoutId) {
    fetchCheckoutDetail()
  }
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取退房详情
const fetchCheckoutDetail = async () => {
  if (!props.checkoutId) return
  
  loading.value = true
  try {
    const response = await get(`customer-service/checkout/detail/${props.checkoutId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取退房详情失败:', error)
    ElMessage.error('获取退房详情失败')
  } finally {
    loading.value = false
  }
}

// 获取退房状态标签类型
const getCheckoutStatusType = (status) => {
  const typeMap = {
    '延期退房': 'warning',
    '正常退房': 'success',
    '提前退房': 'info',
  }
  return typeMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status) => {
  const statusMap = {
    'UNPAID': '未支付',
    'PARTIAL_PAID': '部分支付',
    'PAID': '已支付',
    'FULL_PAID': '已支付',
    'REFUNDED': '已退款',
    'OVERDUE': '逾期'
  }
  return statusMap[status] || status || '未知'
}

// 获取支付状态标签类型
const getPaymentStatusType = (status) => {
  const typeMap = {
    'UNPAID': 'danger',
    'PARTIAL_PAID': 'warning',
    'PAID': 'success',
    'FULL_PAID': 'success',
    'REFUNDED': 'info',
    'OVERDUE': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取支付方式文本
const getPaymentMethodText = (method) => {
  const methodMap = {
    'WECHAT_PAY': '微信支付',
    'ALIPAY': '支付宝',
    'BANK_CARD': '银行卡',
    'CASH': '现金',
    'TRANSFER': '转账'
  }
  return methodMap[method] || method
}

// 获取剩余金额颜色
const getRemainingAmountColor = (amount) => {
  const numAmount = parseFloat(amount || 0)
  if (numAmount > 0) {
    return 'text-red-600'    // 大于0：红色（需要补交）
  } else if (numAmount === 0) {
    return 'text-green-600'  // 等于0：绿色（已结清）
  } else {
    return 'text-yellow-600' // 小于0：黄色（需要退款）
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  detailData.value = null
}
</script>

<style scoped>
.checkout-detail-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.checkout-detail-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.detail-section {
  transition: all 0.3s ease;
}

.detail-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.detail-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  min-width: 120px;
}

.detail-value {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
  flex: 1;
  text-align: right;
}

/* 费用明细卡片样式 */
.cost-detail-card {
  transition: all 0.3s ease;
}

.cost-detail-card:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.cost-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.cost-value {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

/* 剩余金额颜色优先级 */
.cost-value.text-red-600 {
  color: #dc2626 !important;
}

.cost-value.text-green-600 {
  color: #16a34a !important;
}

.cost-value.text-yellow-600 {
  color: #ca8a04 !important;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
