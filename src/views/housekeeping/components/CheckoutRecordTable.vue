<template>
  <div class="checkout-record-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <List />
          </el-icon>
          退房记录列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ pagination.total_count }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="data"
      :loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="rid" label="退房记录ID" min-width="180" fixed="left">
        <template #default="{ row }">
          <span class="font-mono text-sm">{{ row.rid }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="aid" label="入住记录ID" min-width="180">
        <template #default="{ row }">
          <span class="font-mono text-sm text-gray-600">{{ row.aid }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="maternity_name" label="产妇姓名" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.maternity_name.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.maternity_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="room_number" label="房间号" min-width="100">
        <template #default="{ row }">
          <el-tag type="success" size="small">
            {{ row.room_number }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="check_in_date" label="入住日期" min-width="120">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.check_in_date }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="expected_checkout_date" label="预计退房日期" min-width="130">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.expected_checkout_date }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="actual_checkout_date" label="实际退房日期" min-width="130">
        <template #default="{ row }">
          <span class="text-green-600 font-medium">{{ row.actual_checkout_date }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="checkout_status" label="退房状态" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getCheckoutStatusType(row.checkout_status)" size="small">
            {{ row.checkout_status }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="120" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click.stop="emit('view', row)">
              查看详情
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total_count"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { List } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      page_size: 20,
      total_count: 0,
      total_page: 0,
    }),
  },
})

const emit = defineEmits(['view', 'size-change', 'current-change', 'row-click'])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)

// 监听分页数据变化
watch(() => props.pagination, (newPagination) => {
  currentPage.value = newPagination.page
  pageSize.value = newPagination.page_size
}, { immediate: true })

// 获取退房状态类型
const getCheckoutStatusType = (status) => {
  const statusMap = {
    '延期退房': 'warning',
    '正常退房': 'success',
    '提前退房': 'info',
  }
  return statusMap[status] || 'info'
}

// 行点击处理
const handleRowClick = (row) => {
  emit('row-click', row)
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('size-change', size)
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('current-change', page)
}
</script>

<style scoped>
.checkout-record-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.checkout-record-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}
</style>
