<template>
  <div class="checkout-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <List />
          </el-icon>
          退房管理列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="data"
      :loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="id" label="入住号" min-width="140" fixed="left">
        <template #default="{ row }">
          <span class="font-mono">{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="customerName" label="客户姓名" min-width="150">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.customerName.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.customerName }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="roomNumber" label="房间号" min-width="100">
        <template #default="{ row }">
          <el-tag type="success" size="small">
            {{ row.roomNumber }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="originalCheckoutDate" label="原定退房日期" min-width="130">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.originalCheckoutDate }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="actualCheckoutDate" label="实际退房日期" min-width="130">
        <template #default="{ row }">
          <div class="text-sm">
            <span
              v-if="row.actualCheckoutDate && row.actualCheckoutDate !== '-'"
              class="text-green-600 font-medium"
            >
              {{ row.actualCheckoutDate }}
            </span>
            <span v-else class="text-gray-400">{{ row.actualCheckoutDate || '-' }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="260" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <!-- 待退房状态的操作 -->
            <template v-if="row.status === 'pending_checkout'">
              <el-button
                type="primary"
                size="small"
                @click.stop="emit('checkout', row)"
                class="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
              >
                办理退房
              </el-button>
            </template>

            <!-- 已退房状态的操作 -->
            <template v-if="row.status === 'checked_out'">
              <!-- <el-button
                type="info"
                size="small"
                @click.stop="emit('print', row)"
                class="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
              >
                打印结算单
              </el-button> -->
            </template>

            <!-- 延期续住状态的操作 -->
            <template v-if="row.status === 'extended'">
              <el-button
                type="primary"
                size="small"
                @click.stop="emit('checkout', row)"
                class="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
              >
                办理退房
              </el-button>
            </template>

            <!-- 通用操作 -->
            <el-button type="default" size="small" @click.stop="emit('view', row)">
              查看
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { List } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['checkout', 'extend', 'view', 'print', 'pagination-change', 'row-click'])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = computed(() => props.data.length)

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending_checkout: 'warning',
    checked_out: 'success',
    early_checkout: 'info',
    extended: 'primary',
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending_checkout: '待退房',
    checked_out: '已退房',
    early_checkout: '提前退房',
    extended: '延期续住',
  }
  return statusMap[status] || '未知'
}

// 行点击处理
const handleRowClick = (row) => {
  emit('row-click', row)
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('pagination-change', { page: currentPage.value, size })
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('pagination-change', { page, size: pageSize.value })
}
</script>

<style scoped>
.checkout-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.checkout-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

/* 自定义分页样式 */

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}
</style>
